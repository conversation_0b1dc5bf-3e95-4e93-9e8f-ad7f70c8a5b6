{% extends "base.html" %}

{% block title %}Hello World - Flask App{% endblock %}

{% block content %}
<h1>🎉 Hello World! 🎉</h1>
<p>Welcome to your Flask application! This is a simple "Hello World" page to get you started.</p>

<div style="margin: 30px 0; padding: 20px; background-color: #e7f3ff; border-left: 4px solid #007bff; border-radius: 5px;">
    <h3>🚀 Your Flask App is Running!</h3>
    <p>Congratulations! You've successfully set up a basic Flask application. Here's what you have:</p>
    <ul>
        <li>✅ Flask app structure with blueprints</li>
        <li>✅ Basic routing system</li>
        <li>✅ Template inheritance</li>
        <li>✅ Simple navigation</li>
    </ul>
</div>

<div style="margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
    <h3>📝 Next Steps</h3>
    <p>Now that your basic app is working, you can:</p>
    <ul>
        <li>Add more routes and pages</li>
        <li>Implement database models</li>
        <li>Add user authentication</li>
        <li>Build your code snippet management features</li>
    </ul>
</div>

<p style="text-align: center; margin-top: 30px;">
    <a href="{{ url_for('main.hello') }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        Try the Hello Route →
    </a>
</p>
{% endblock %}