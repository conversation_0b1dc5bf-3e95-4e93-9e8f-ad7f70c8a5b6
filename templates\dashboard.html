{% extends "layout.html" %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<div class="w-full">
  <!-- Dashboard Header -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-16">
    <div class="floating">
      <h1 class="font-display text-5xl lg:text-7xl mb-4 gradient-text text-glow">Welcome back, {{ user.username }}!</h1>
      <p class="font-body text-xl text-[var(--text-secondary)] opacity-90 max-w-2xl">Manage your profile and track your coding activity with our premium dashboard experience</p>
    </div>
    <div class="mt-8 lg:mt-0">
      <a href="{{ url_for('auth.create_snippet') }}" class="btn-primary liquid-btn micro-bounce shimmer">
        <i class="fa-solid fa-plus mr-3"></i>Create New Snippet
      </a>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
    <!-- Total Snippets -->
    <div class="stat-card blue card-3d shimmer p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-subheading text-white/90 text-sm uppercase tracking-wider mb-3">Total Snippets</p>
          <p class="font-display text-5xl font-black mb-2 text-glow-white">{{ stats.total_snippets }}</p>
          <p class="font-caption text-white/80">All your code snippets</p>
        </div>
        <div class="glass-card p-6 rounded-2xl">
          <i class="fa-solid fa-code text-4xl text-glow-white rotate-scale"></i>
        </div>
      </div>
    </div>

    <!-- Public Snippets -->
    <div class="stat-card emerald card-3d shimmer p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-subheading text-white/90 text-sm uppercase tracking-wider mb-3">Public</p>
          <p class="font-display text-5xl font-black mb-2 text-glow-white">{{ stats.public_snippets }}</p>
          <p class="font-caption text-white/80">Shared with community</p>
        </div>
        <div class="glass-card p-6 rounded-2xl">
          <i class="fa-solid fa-globe text-4xl text-glow-white rotate-scale"></i>
        </div>
      </div>
    </div>

    <!-- Private Snippets -->
    <div class="stat-card violet card-3d shimmer p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-subheading text-white/90 text-sm uppercase tracking-wider mb-3">Private</p>
          <p class="font-display text-5xl font-black mb-2 text-glow-white">{{ stats.private_snippets }}</p>
          <p class="font-caption text-white/80">Personal collection</p>
        </div>
        <div class="glass-card p-6 rounded-2xl">
          <i class="fa-solid fa-lock text-4xl text-glow-white rotate-scale"></i>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="stat-card amber card-3d shimmer p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <p class="font-subheading text-white/90 text-sm uppercase tracking-wider mb-3">Last 30 Days</p>
          <p class="font-display text-5xl font-black mb-2 text-glow-white">{{ stats.recent_snippets }}</p>
          <p class="font-caption text-white/80">Recent activity</p>
        </div>
        <div class="glass-card p-6 rounded-2xl">
          <i class="fa-solid fa-chart-line text-4xl text-glow-white rotate-scale"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
    <!-- Profile Section -->
    <div class="xl:col-span-1">
      <div class="glass-card morphing-border p-10">
        <div class="form-section-header mb-10">
          <i class="fa-solid fa-user-circle text-3xl gradient-text"></i>
          <h3 class="font-heading text-2xl gradient-text">Profile Overview</h3>
        </div>

        <!-- Profile Avatar and Info -->
        <div class="flex flex-col items-center mb-10">
          <div class="relative floating">
            <div class="w-36 h-36 rounded-full flex items-center justify-center border-4 mb-6 pulse-glow transition-all duration-500 hover:scale-110" style="background: var(--gradient-primary); border-color: rgba(255,255,255,0.3); color: #fff; font-size: 4rem; box-shadow: var(--glow-primary);">
              {{ user.username[0].upper() if user.username else 'U' }}
            </div>
            <div class="absolute -bottom-2 -right-2 w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-4 border-white flex items-center justify-center shadow-lg pulse-glow">
              <i class="fa-solid fa-check text-white text-lg"></i>
            </div>
          </div>
          <h4 class="font-heading text-2xl font-bold text-[var(--text-primary)] mb-3">{{ user.username }}</h4>
          <p class="font-body text-[var(--text-secondary)] mb-4 text-lg">{{ user.email }}</p>
          {% if stats.most_used_language %}
            <div class="glass-card px-6 py-3 rounded-full">
              <p class="gradient-text font-semibold">
                <i class="fa-solid fa-code mr-2"></i>{{ stats.most_used_language.title() }} Developer
              </p>
            </div>
          {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="space-y-4">
          <a href="{{ url_for('auth.snippets') }}" class="btn-primary w-full quick-action-btn micro-bounce">
            <i class="fa-solid fa-list mr-3"></i>Browse All Snippets
          </a>
          <a href="{{ url_for('auth.my_snippets') }}" class="btn-secondary w-full quick-action-btn micro-bounce">
            <i class="fa-solid fa-file-code mr-3"></i>My Collection
          </a>
          <a href="{{ url_for('auth.create_snippet') }}" class="btn-success w-full quick-action-btn micro-bounce">
            <i class="fa-solid fa-plus mr-3"></i>Create New Snippet
          </a>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="xl:col-span-2">
      <div class="dashboard-card bg-gradient-to-br from-[var(--bg-card)] to-[var(--bg-card-alt)]/30 p-6 rounded-xl shadow-lg">
        <div class="form-section-header">
          <i class="fa-solid fa-clock text-xl text-[var(--accent-purple)]"></i>
          <h3 class="text-xl font-semibold">Recent Activity</h3>
        </div>

        {% if recent_snippets %}
          <div class="space-y-3">
            {% for snippet in recent_snippets %}
              <div class="activity-item group">
                <div class="flex items-center justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-[var(--accent-blue)] to-[var(--accent-purple)] flex items-center justify-center text-white font-bold text-sm">
                        {{ snippet.language[0].upper() if snippet.language else 'C' }}
                      </div>
                      <div class="flex-1">
                        <h4 class="font-semibold text-[var(--text-primary)] group-hover:text-[var(--accent-blue)] transition-colors">{{ snippet.title }}</h4>
                        <div class="flex items-center space-x-3 mt-1">
                          <span class="text-xs text-[var(--text-secondary)] flex items-center">
                            <i class="fa-solid fa-calendar mr-1"></i>{{ snippet.created_at.strftime('%b %d, %Y') }}
                          </span>
                          <span class="text-xs px-2 py-1 bg-[var(--accent-blue)]/15 text-[var(--accent-blue)] rounded-full font-medium">
                            {{ snippet.language.title() }}
                          </span>
                          <span class="text-xs px-2 py-1 {{ 'bg-emerald-500/15 text-emerald-600' if snippet.is_public else 'bg-slate-500/15 text-slate-600' }} rounded-full font-medium">
                            <i class="fa-solid {{ 'fa-globe' if snippet.is_public else 'fa-lock' }} mr-1"></i>
                            {{ 'Public' if snippet.is_public else 'Private' }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <a href="{{ url_for('auth.view_snippet', id=snippet.id) }}" class="ml-4 px-4 py-2 text-sm bg-[var(--accent-blue)] text-white rounded-lg hover:bg-[var(--hover-blue)] transition-all duration-200 opacity-0 group-hover:opacity-100">
                    <i class="fa-solid fa-eye mr-1"></i>View
                  </a>
                </div>
              </div>
            {% endfor %}
          </div>

          <div class="mt-8 text-center">
            <a href="{{ url_for('auth.my_snippets') }}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[var(--accent-blue)] to-[var(--accent-purple)] text-white rounded-lg hover:shadow-lg transition-all duration-200 font-medium">
              <i class="fa-solid fa-list mr-2"></i>View All Snippets
              <i class="fa-solid fa-arrow-right ml-2"></i>
            </a>
          </div>
        {% else %}
          <div class="text-center py-12">
            <div class="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-[var(--accent-blue)]/20 to-[var(--accent-purple)]/20 flex items-center justify-center">
              <i class="fa-solid fa-code text-3xl text-[var(--accent-blue)]"></i>
            </div>
            <h4 class="text-lg font-semibold text-[var(--text-primary)] mb-2">No snippets yet</h4>
            <p class="text-[var(--text-secondary)] mb-6">Start building your code collection today!</p>
            <a href="{{ url_for('auth.create_snippet') }}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-lg hover:shadow-lg transition-all duration-200 font-medium">
              <i class="fa-solid fa-plus mr-2"></i>Create Your First Snippet
            </a>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Profile Management Section -->
  <div class="dashboard-card bg-gradient-to-br from-[var(--bg-card)] to-[var(--bg-card-alt)]/30 p-8 rounded-xl shadow-lg">
    <div class="form-section-header mb-8">
      <i class="fa-solid fa-user-gear text-2xl"></i>
      <h3 class="text-2xl font-bold">Profile Management</h3>
    </div>

    <form method="POST" enctype="multipart/form-data" class="space-y-8">
      {{ form.hidden_tag() }}

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Account Information -->
        <div class="enhanced-form-section">
          <div class="form-section-header">
            <i class="fa-solid fa-user-pen text-lg"></i>
            <h4 class="text-lg font-semibold">Account Information</h4>
          </div>
          <div class="space-y-5">
            <div class="enhanced-input-group">
              {{ form.username.label(class_='block mb-2 text-sm font-semibold text-[var(--text-primary)]') }}
              <div class="relative">
                <div class="input-icon">
                  <i class="fa-solid fa-user"></i>
                </div>
                {{ form.username(class_='enhanced-input', placeholder='Your username') }}
              </div>
              {% if form.username.errors %}
                <div class="text-red-400 text-xs mt-2">
                  {% for error in form.username.errors %}
                    <p class="flex items-center"><i class="fa-solid fa-exclamation-circle mr-1"></i>{{ error }}</p>
                  {% endfor %}
                </div>
              {% endif %}
            </div>

            <div class="enhanced-input-group">
              {{ form.email.label(class_='block mb-2 text-sm font-semibold text-[var(--text-primary)]') }}
              <div class="relative">
                <div class="input-icon">
                  <i class="fa-solid fa-envelope"></i>
                </div>
                {{ form.email(class_='enhanced-input', placeholder='Your email address') }}
              </div>
              {% if form.email.errors %}
                <div class="text-red-400 text-xs mt-2">
                  {% for error in form.email.errors %}
                    <p class="flex items-center"><i class="fa-solid fa-exclamation-circle mr-1"></i>{{ error }}</p>
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
        </div>



      <!-- Password Update -->
      <div class="bg-[var(--bg-card)]/70 p-4 md:p-6 rounded-xl border border-[var(--bg-card-alt)] shadow-lg md:col-span-2 xl:col-span-1">
        <h3 class="text-xl mb-4 border-b border-[var(--bg-card-alt)] pb-2">
          <i class="fa-solid fa-key mr-2"></i>Change Password <span class="text-xs text-[var(--text-secondary)]">(Optional)</span>
        </h3>
        <div class="grid grid-cols-1 gap-4">
          <div class="w-full">
            {{ form.password.label(class_='block mb-2 text-sm') }}
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]">
                <i class="fa-solid fa-lock"></i>
              </span>
              {{ form.password(class_='w-full pl-10', placeholder='Leave blank to keep current password') }}
              <button type="button" class="password-toggle-btn" aria-label="Toggle password visibility">
                <i class="fa-solid fa-eye"></i>
              </button>
            </div>
            {% if form.password.errors %}
              <div class="text-red-500 text-xs mt-1">
                {% for error in form.password.errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
          <div class="w-full">
            {{ form.confirm_password.label(class_='block mb-2 text-sm') }}
            <div class="relative w-full">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-[var(--accent-blue)]">
                <i class="fa-solid fa-shield-halved"></i>
              </span>
              {{ form.confirm_password(class_='w-full pl-10', placeholder='Confirm new password if changing') }}
              <button type="button" class="password-toggle-btn" aria-label="Toggle password visibility">
                <i class="fa-solid fa-eye"></i>
              </button>
            </div>
            {% if form.confirm_password.errors %}
              <div class="text-red-500 text-xs mt-1">
                {% for error in form.confirm_password.errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

      <div class="flex justify-center mt-10">
        <button type="submit" class="btn-primary px-10 py-4 text-lg font-bold shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
          <i class="fa-solid fa-floppy-disk mr-3"></i>Update Profile
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    const passwordToggles = document.querySelectorAll('.password-toggle-btn');
    passwordToggles.forEach(function(toggle) {
      toggle.addEventListener('click', function(e) {
        e.preventDefault();
        const passwordField = this.previousElementSibling;
        const icon = this.querySelector('i');

        if (passwordField.type === 'password') {
          passwordField.type = 'text';
          icon.classList.remove('fa-eye');
          icon.classList.add('fa-eye-slash');
        } else {
          passwordField.type = 'password';
          icon.classList.remove('fa-eye-slash');
          icon.classList.add('fa-eye');
        }
      });
    });
  });
</script>
{% endblock %}
